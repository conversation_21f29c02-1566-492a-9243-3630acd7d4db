import mongoose from "mongoose"

export const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URL
    
    if (!mongoURI) {
      throw new Error("MONGODB_URL environment variable is not defined")
    }

    const conn = await mongoose.connect(mongoURI)
    
    console.log(`MongoDB Connected: ${conn.connection.host}`)
  } catch (error) {
    console.error("Error connecting to MongoDB:", error)
    process.exit(1)
  }
}
