import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import type { Route } from "./+types/register"

import { Button } from "~/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card"
import { Input } from "~/components/ui/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form"

// Metadata for the page
export function meta({}: Route.MetaArgs) {
  return [
    { title: "Register - Create Account" },
    {
      name: "description",
      content:
        "Create a new account by filling out the registration form with your personal information.",
    },
    {
      name: "keywords",
      content: "register, signup, create account, user registration, Pakistan",
    },
  ]
}

// Zod schema for form validation
const registerSchema = z.object({
  fullName: z
    .string()
    .min(1, "Full name is required")
    .min(2, "Full name must be at least 2 characters"),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  phoneNumber: z
    .string()
    .min(1, "Phone number is required")
    .regex(/^\d{10}$/, "Phone number must be exactly 10 digits (without +92)"),
  dateOfBirth: z
    .string()
    .min(1, "Date of birth is required")
    .refine((date) => {
      const parsedDate = new Date(date)
      if (isNaN(parsedDate.getTime())) return false

      const today = new Date()
      const age = today.getFullYear() - parsedDate.getFullYear()
      const monthDiff = today.getMonth() - parsedDate.getMonth()
      const dayDiff = today.getDate() - parsedDate.getDate()

      // Check if birthday has occurred this year
      const actualAge =
        monthDiff < 0 || (monthDiff === 0 && dayDiff < 0) ? age - 1 : age

      return actualAge >= 12 && parsedDate < today
    }, "You must be at least 12 years old and enter a valid date"),
  address: z
    .string({ required_error: "Address is required" })
    .min(5, "Address must be at least 5 characters"),
})

type RegisterFormData = z.infer<typeof registerSchema>

export default function Component() {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phoneNumber: "",
      dateOfBirth: "",
      address: "",
    },
  })

  const onSubmit = async (data: RegisterFormData) => {
    setIsLoading(true)

    try {
      // Add +92 prefix to phone number before sending to backend
      const submitData = {
        ...data,
        phoneNumber: `+92${data.phoneNumber}`,
      }

      const response = await fetch("/api/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      })

      const result = await response.json()

      if (response.ok) {
        toast.success("Account created successfully!", {
          description:
            "Your registration has been completed. You can now view your details on the dashboard.",
          action: {
            label: "Go to Dashboard",
            onClick: () => (window.location.href = "/dashboard"),
          },
        })
        form.reset()
      } else {
        toast.error("Registration failed", {
          description:
            result.message || "Failed to create account. Please try again.",
        })
      }
    } catch (error) {
      toast.error("Network error", {
        description:
          "Unable to connect to the server. Please check your connection and try again.",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">Create Account</CardTitle>
          <CardDescription>
            Fill in your information to register for an account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your full name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter your email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number (Pakistan)</FormLabel>
                    <FormControl>
                      <div className="flex">
                        <span className="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border border-r-0 border-gray-300 rounded-l-md">
                          +92
                        </span>
                        <Input
                          type="tel"
                          placeholder="3001234567"
                          className="rounded-l-none"
                          maxLength={10}
                          {...field}
                          onChange={(e) => {
                            // Only allow digits
                            const value = e.target.value.replace(/\D/g, "")
                            field.onChange(value)
                          }}
                        />
                      </div>
                    </FormControl>
                    <p className="text-xs text-gray-600 mt-1">
                      Enter 10 digits without +92 (e.g., 3001234567)
                    </p>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dateOfBirth"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date of Birth</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <p className="text-xs text-gray-600 mt-1">
                      Select your date of birth. You must be at least 12 years
                      old to register.
                    </p>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Creating Account..." : "Create Account"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
