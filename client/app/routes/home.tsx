import { <PERSON> } from "react-router"
import type { Route } from "./+types/home"
import { <PERSON><PERSON> } from "~/components/ui/button"
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "~/components/ui/card"

export function meta({}: Route.MetaArgs) {
  return [
    { title: "MERN Stack Task" },
    { name: "description", content: "MERN Stack Task" },
  ]
}

export default function Home() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">MERN Stack Task</CardTitle>
          <CardDescription className="text-base leading-relaxed">
            This is a simple MERN stack job task. Users can register through a
            form, view all registered users on a dashboard, and download the
            user data as a PDF.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button asChild className="w-full">
              <Link to="/register">Register User</Link>
            </Button>
            <Button asChild variant="outline" className="w-full">
              <Link to="/dashboard">View Dashboard</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
